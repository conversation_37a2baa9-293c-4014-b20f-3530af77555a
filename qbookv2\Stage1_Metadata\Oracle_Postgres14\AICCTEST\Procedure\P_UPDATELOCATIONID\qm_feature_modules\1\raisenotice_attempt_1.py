import re

def raisenotice(data, schema):
    space_str = re.sub(r' +', ' ', data)
    space_str_split = space_str.split('\n')
    for i in space_str_split:
        if 'raise notice' in i:
            space_str_replace = re.sub(r'%', '% ', i, re.DOTALL)
            space_str = space_str.replace(i, space_str_replace)
        else:
            space_str = space_str
    data = space_str
    data = re.sub(r'\braise\s*notice\s*execute\b', 'RAISE NOTICE ', data, flags=re.DOTALL | re.I)
    # Enhanced logic to handle additional patterns
    data = re.sub(r'\braise\s*notice\b', 'RAISE NOTICE ', data, flags=re.DOTALL | re.I)
    return data