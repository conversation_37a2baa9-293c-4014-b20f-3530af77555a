"""
Prompts for module enhancement in Stage 2 conversion analysis.
"""

def create_module_enhancement_prompt(
    original_module_code: str,
    qmigrator_target_statement: str,
    ai_corrected_statement: str,
    deployment_error: str,
    ai_comparison_feedback: str = None,
    attempt_history: dict = None,
    migration_name: str = None,
    feature_name: str = None,
    keywords: str = None
) -> str:
    """
    Create a generic preserve-first analysis prompt for enhancing Python modules additively.

    This prompt focuses on preserving existing functionality while adding supplementary
    logic to bridge the transformation gap between current and expected output.

    Args:
        original_module_code: Current working Python module (DO NOT MODIFY)
        qmigrator_target_statement: Current output from existing module
        ai_corrected_statement: Expected output that we need to achieve
        deployment_error: Error context from deployment
        ai_comparison_feedback: Feedback from AI comparison (optional)
        attempt_history: Previous enhancement attempts with their outcomes (optional)
        migration_name: Migration name for database-specific context (optional)
        feature_name: Feature name from keyword mapping (optional)
        keywords: Keywords associated with this feature (optional)

    Returns:
        Formatted prompt string for additive module enhancement
    """

    # Build feedback section
    feedback_section = ""
    if ai_comparison_feedback:
        feedback_section = f"\nAI COMPARISON FEEDBACK:\n{ai_comparison_feedback}\n"

    # Build hybrid attempt history section for efficient learning
    attempt_history_section = ""
    if attempt_history and isinstance(attempt_history, dict) and (attempt_history.get('recent_attempts') or attempt_history.get('failure_patterns')):
        attempt_history_section = "\nATTEMPT HISTORY LEARNING (HYBRID APPROACH):\n"
        attempt_history_section += "=" * 70 + "\n"

        # Show all detailed attempts (no summarization)
        all_attempts = attempt_history.get('all_attempts', [])
        if all_attempts:
            attempt_history_section += f"\nALL PREVIOUS ATTEMPTS ({len(all_attempts)} total):\n"
            for attempt in all_attempts:
                attempt_num = attempt.get('attempt_number', 'Unknown')
                modules_used = attempt.get('modules_used', [])
                ai_feedback = attempt.get('ai_feedback', 'No feedback available')

                attempt_history_section += f"\nAttempt {attempt_num} - {len(modules_used)} modules:\n"

                # Safely extract module names with error handling
                module_names = []
                for m in modules_used:
                    if isinstance(m, dict):
                        module_name = m.get('module_name', 'Unknown')
                        if isinstance(module_name, str):
                            module_names.append(module_name)
                        else:
                            module_names.append(str(module_name))
                    else:
                        module_names.append(str(m))

                attempt_history_section += f"  Modules: {', '.join(module_names)}\n"
                attempt_history_section += f"  Why it failed: {ai_feedback[:200]}...\n"

        total_attempts = attempt_history.get('total_attempts', 0)
        attempt_history_section += f"\nTotal attempts analyzed: {total_attempts}\n"

        attempt_history_section += "\nSMART LEARNING INSTRUCTIONS:\n"
        attempt_history_section += "- ANALYZE what supplementary approaches were tried and why they failed\n"
        attempt_history_section += "- IDENTIFY patterns in failed additive logic attempts\n"
        attempt_history_section += "- TRY fundamentally different supplementary transformation strategies\n"
        attempt_history_section += "- AVOID repeating the same additive logic patterns that already failed\n"
        attempt_history_section += "- FOCUS on different gap-bridging approaches not yet attempted\n"
        attempt_history_section += "- LEARN from failed supplementary logic to design better additive solutions\n"
        attempt_history_section += "=" * 70 + "\n"

    # Build feature context section
    feature_context_section = ""
    if feature_name or keywords or migration_name:
        feature_context_section = f"""
FEATURE CONTEXT FROM KEYWORD MAPPING:
====================================
"""
        if migration_name:
            feature_context_section += f"Migration: {migration_name}\n"
        if feature_name:
            feature_context_section += f"Feature Name: {feature_name}\n"
        if keywords:
            feature_context_section += f"Associated Keywords: {keywords}\n"
        feature_context_section += "\n"

    prompt = f"""
You are an expert Python developer. Your ULTIMATE GOAL is to modify the Python module to transform the CURRENT OUTPUT into the AI CORRECTED OUTPUT exactly.

TRANSFORMATION TASK:
===================

CURRENT PYTHON MODULE:
{original_module_code}

CURRENT OUTPUT (what the module produces now):
{qmigrator_target_statement}

AI CORRECTED OUTPUT (what it MUST produce):
{ai_corrected_statement}

DEPLOYMENT ERROR CONTEXT:
{deployment_error}

{feedback_section}
{attempt_history_section}
{feature_context_section}

SIMPLE 3-STEP APPROACH:
======================

STEP 1: COMPARE OUTPUTS
- Compare CURRENT OUTPUT vs AI CORRECTED OUTPUT
- Identify EXACTLY what SQL transformation is needed
- Ignore formatting differences (whitespace, tabs, spaces, brackets, case)
- Focus ONLY on the core SQL logic that needs to change

STEP 2: PRESERVE + ENHANCE
- KEEP all existing module functionality (DO NOT break old scenarios)
- ADD new logic on top of existing code to handle the transformation gap
- Ensure the module can handle both old scenarios AND the new requirement

STEP 3: TRANSFORM TO TARGET
- Modify the Python module to produce AI CORRECTED OUTPUT exactly
- The enhanced module MUST transform the input to match AI CORRECTED format
- Handle all variations and scenarios of the same SQL feature

CORE PRINCIPLES:
===============

PRESERVE EXISTING FUNCTIONALITY:
- DO NOT modify or remove existing working code
- DO NOT break backward compatibility
- KEEP all current transformations that work correctly
- ADD new logic on top of existing functionality

FOCUS ON THE TRANSFORMATION GAP:
- Identify WHY current output differs from AI corrected output
- Add ONLY the missing transformation logic needed
- Ignore minor formatting differences (spaces, case, brackets)
- Focus on core SQL logic differences

ENSURE ACTUAL TRANSFORMATION:
- The module MUST actually transform the input (not show "No transformation applied")
- The output MUST match AI corrected format exactly
- Handle all variations of the same SQL feature generically
- Test that patterns actually match the input format

IMPLEMENTATION STRATEGY:
=======================

ADDITIVE APPROACH:
```python
def existing_function_name(input_statement, schema_name):
    # STEP 1: Keep existing logic (PRESERVE all current functionality)
    current_output = existing_transformation_logic(input_statement, schema_name)

    # STEP 2: Add transformation to bridge the gap to AI corrected output
    if needs_additional_transformation(current_output):
        final_output = apply_ai_corrected_transformation(current_output)
        return final_output

    # STEP 3: Return existing output if no additional transformation needed
    return current_output
```

REGEX SAFETY RULES:
==================
- ALWAYS use raw strings: r"pattern"
- NEVER use backreferences (\\1, \\2) without capturing groups ()
- USE re.IGNORECASE and re.DOTALL flags for flexibility
- ESCAPE special characters with re.escape() when needed
- TEST patterns mentally before implementation

VALID REGEX PATTERNS:
- With capturing group: r'(pattern)' and replacement r'\\1'
- Without backreferences: r'pattern' and replacement r'new_text'
- Case-insensitive: flags=re.IGNORECASE
- Flexible whitespace: \\s* (optional) or \\s+ (required)

SUCCESS CRITERIA:
================
- Enhanced module MUST produce AI corrected output exactly
- Ignore formatting differences (whitespace, tabs, spaces, brackets, case)
- Focus on core SQL logic transformation
- Module MUST show "Statement transformed" in logs (not "No transformation applied")
- Preserve all existing functionality for backward compatibility

OUTPUT REQUIREMENTS:
===================

Provide a JSON response with the enhanced Python module:

{{
  "enhanced_code": "Complete enhanced Python module that produces AI corrected output exactly",
  "analysis": "Brief explanation of: 1) What transformation gap was identified, 2) What logic was added to bridge the gap, 3) How the enhanced module produces AI corrected output, 4) Confirmation that existing functionality is preserved"
}}

CRITICAL SUCCESS FACTORS:
========================
- PRESERVE all existing module functionality completely
- ADD logic to transform current output into AI corrected output
- ENSURE module actually applies transformation (shows "Statement transformed")
- IGNORE formatting differences (focus on core SQL logic)
- HANDLE all variations of the same SQL feature
- NEVER use invalid regex patterns or syntax errors
- ULTIMATE GOAL: Transform to AI corrected format exactly


"""

    return prompt
