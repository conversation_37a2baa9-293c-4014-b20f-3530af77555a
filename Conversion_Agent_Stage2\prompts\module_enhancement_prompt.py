"""
Prompts for module enhancement in Stage 2 conversion analysis.
"""

def create_module_enhancement_prompt(
    original_module_code: str,
    qmigrator_target_statement: str,
    ai_corrected_statement: str,
    deployment_error: str,
    ai_comparison_feedback: str = None,
    attempt_history: dict = None,
    migration_name: str = None,
    feature_name: str = None,
    keywords: str = None
) -> str:
    """
    Create a generic preserve-first analysis prompt for enhancing Python modules additively.

    This prompt focuses on preserving existing functionality while adding supplementary
    logic to bridge the transformation gap between current and expected output.

    Args:
        original_module_code: Current working Python module (DO NOT MODIFY)
        qmigrator_target_statement: Current output from existing module
        ai_corrected_statement: Expected output that we need to achieve
        deployment_error: Error context from deployment
        ai_comparison_feedback: Feedback from AI comparison (optional)
        attempt_history: Previous enhancement attempts with their outcomes (optional)
        migration_name: Migration name for database-specific context (optional)
        feature_name: Feature name from keyword mapping (optional)
        keywords: Keywords associated with this feature (optional)

    Returns:
        Formatted prompt string for additive module enhancement
    """

    # Build feedback section
    feedback_section = ""
    if ai_comparison_feedback:
        feedback_section = f"\nAI COMPARISON FEEDBACK:\n{ai_comparison_feedback}\n"

    # Build hybrid attempt history section for efficient learning
    attempt_history_section = ""
    if attempt_history and isinstance(attempt_history, dict) and (attempt_history.get('recent_attempts') or attempt_history.get('failure_patterns')):
        attempt_history_section = "\nATTEMPT HISTORY LEARNING (HYBRID APPROACH):\n"
        attempt_history_section += "=" * 70 + "\n"

        # Show all detailed attempts (no summarization)
        all_attempts = attempt_history.get('all_attempts', [])
        if all_attempts:
            attempt_history_section += f"\nALL PREVIOUS ATTEMPTS ({len(all_attempts)} total):\n"
            for attempt in all_attempts:
                attempt_num = attempt.get('attempt_number', 'Unknown')
                modules_used = attempt.get('modules_used', [])
                ai_feedback = attempt.get('ai_feedback', 'No feedback available')

                attempt_history_section += f"\nAttempt {attempt_num} - {len(modules_used)} modules:\n"

                # Safely extract module names with error handling
                module_names = []
                for m in modules_used:
                    if isinstance(m, dict):
                        module_name = m.get('module_name', 'Unknown')
                        if isinstance(module_name, str):
                            module_names.append(module_name)
                        else:
                            module_names.append(str(module_name))
                    else:
                        module_names.append(str(m))

                attempt_history_section += f"  Modules: {', '.join(module_names)}\n"
                attempt_history_section += f"  Why it failed: {ai_feedback[:200]}...\n"

        total_attempts = attempt_history.get('total_attempts', 0)
        attempt_history_section += f"\nTotal attempts analyzed: {total_attempts}\n"

        attempt_history_section += "\nSMART LEARNING INSTRUCTIONS:\n"
        attempt_history_section += "- ANALYZE what supplementary approaches were tried and why they failed\n"
        attempt_history_section += "- IDENTIFY patterns in failed additive logic attempts\n"
        attempt_history_section += "- TRY fundamentally different supplementary transformation strategies\n"
        attempt_history_section += "- AVOID repeating the same additive logic patterns that already failed\n"
        attempt_history_section += "- FOCUS on different gap-bridging approaches not yet attempted\n"
        attempt_history_section += "- LEARN from failed supplementary logic to design better additive solutions\n"
        attempt_history_section += "=" * 70 + "\n"

    # Build feature context section
    feature_context_section = ""
    if feature_name or keywords or migration_name:
        feature_context_section = f"""
FEATURE CONTEXT FROM KEYWORD MAPPING:
====================================
"""
        if migration_name:
            feature_context_section += f"Migration: {migration_name}\n"
        if feature_name:
            feature_context_section += f"Feature Name: {feature_name}\n"
        if keywords:
            feature_context_section += f"Associated Keywords: {keywords}\n"
        feature_context_section += "\n"

    prompt = f"""
You are an expert Python developer. Your ONLY goal is to modify the Python module so it transforms the CURRENT OUTPUT into the REQUIRED OUTPUT exactly.

CORE TASK:
=========

CURRENT PYTHON MODULE:
{original_module_code}

CURRENT OUTPUT (what the module produces now):
{qmigrator_target_statement}

REQUIRED OUTPUT (what it MUST produce):
{ai_corrected_statement}

DEPLOYMENT ERROR CONTEXT:
{deployment_error}

{feedback_section}
{attempt_history_section}
{feature_context_section}

YOUR MISSION:
============

STEP 1: UNDERSTAND THE TRANSFORMATION
- Look at CURRENT OUTPUT vs REQUIRED OUTPUT
- Identify EXACTLY what needs to change
- Ignore formatting differences (spaces, tabs, case, brackets)
- Focus on the core SQL logic transformation needed

STEP 2: ANALYZE THE CURRENT MODULE
- Understand what the current Python module does
- See if it's detecting the right patterns
- Check if it's applying the right transformations
- Identify why it's not producing the REQUIRED OUTPUT

STEP 3: FIX THE MODULE
- Modify the Python module to produce REQUIRED OUTPUT
- Keep existing functionality that works
- Add/modify logic to bridge the gap
- Ensure the module actually transforms the input

SUPPLEMENTARY LOGIC GUIDELINES:
===============================

UNDERSTAND-THEN-ENHANCE APPROACH:
- ANALYZE current module: understand what it does and how it works
- IDENTIFY transformation gap: what's missing to get AI expected output
- DESIGN supplementary logic: additive transformation to bridge the gap
- ENSURE final output: enhanced module produces exact AI expected format

GAP-BRIDGING STRATEGY:
- Take current module output as input to supplementary logic
- Apply transformation to handle missing variations
- Use pattern-based approach that covers scenarios
- Focus on enhancing the transformation capability

PYTHON CODING REQUIREMENTS:
===========================

REGEX PATTERN SAFETY:
- ALWAYS validate regex patterns before using them
- NEVER use backreferences (\1, \2) without corresponding capturing groups ()
- USE raw strings r"pattern" for all regex patterns
- TEST regex patterns mentally before implementation
- ESCAPE special characters properly with re.escape() when needed

VALID REGEX EXAMPLES:
- Capturing group: r'(pattern)' with replacement r'\\1'
- Non-capturing group: r'(?:pattern)'
- Safe replacement: r'new_pattern' (no backreferences)
- Escaped literals: re.escape(literal_string)
- Case-insensitive: re.sub(pattern, replacement, text, flags=re.IGNORECASE)

INVALID REGEX PATTERNS (AVOID):
- r'pattern' with replacement r'\\1' (no capturing group)
- Unescaped special characters in patterns
- Malformed group references
- Missing re.IGNORECASE flag for case-insensitive matching
- Not handling optional whitespace: use \\s* or \\s+ appropriately

COMMON MISTAKES TO AVOID:
- Using \\1 without capturing group: r'pattern' -> r'\\1' ❌
- Correct approach: r'(pattern)' -> r'\\1' ✅
- Forgetting to escape special characters like . + * ? [ ] ( ) {{}} | \\
- Not accounting for optional whitespace between SQL keywords
- Missing flags for case-insensitive and multiline matching

ENHANCEMENT IMPLEMENTATION:
```python
def existing_function_name(input_statement, schema_name):
    # Step 1: Keep existing transformation logic (PRESERVE)
    current_output = existing_logic(input_statement, schema_name)

    # Step 2: Add enhancement (ONLY if gap exists)
    if pattern_detected(current_output):
        enhanced_output = apply_transformation(current_output)
        return enhanced_output

    # Step 3: Return existing output if no enhancement needed
    return current_output
```

PATTERN MATCHING PRINCIPLES:
===========================

FORMATTING-AGNOSTIC APPROACH:
- DESIGN patterns that work regardless of whitespace variations
- HANDLE all possible spacing: spaces, tabs, newlines, mixed combinations
- SUPPORT all case variations: uppercase, lowercase, mixed case
- ACCOUNT for optional elements and different ordering
- PROCESS nested structures with varying complexity levels

ROBUST REGEX CONSTRUCTION GUIDELINES:
- USE flexible whitespace patterns (\\s+ for required, \\s* for optional)
- APPLY case-insensitive matching for SQL keywords
- IMPLEMENT balanced parentheses matching for nested structures
- HANDLE quoted identifiers and string literals appropriately
- SUPPORT schema-qualified names and aliases

ADAPTIVE PATTERN STRATEGY:
- ANALYZE the keywords to understand what constructs to match
- BUILD patterns that capture the essential structure, not specific formatting
- CREATE flexible capturing groups for transformation components
- ENSURE patterns work across different SQL dialects and styles
- VALIDATE patterns against multiple formatting variations

CONTEXT-INDEPENDENT TRANSFORMATION STRATEGY:
- SEARCH for target constructs ANYWHERE in the statement
- AVOID assumptions about statement structure
- FOCUS on the specific SQL construct mentioned in keywords
- TRANSFORM the construct regardless of its surrounding context
- PRESERVE all surrounding code while transforming only the target construct

UNIVERSAL PATTERN MATCHING APPROACH:
- USE global search patterns that work across all SQL statement types
- AVOID context-specific assumptions
- IMPLEMENT construct-specific matching based on keywords only
- HANDLE nested constructs within any SQL statement structure
- ENSURE patterns work in various SQL contexts

TRANSFORMATION METHODOLOGY:
==========================

ENHANCEMENT METHODOLOGY:
- ANALYZE keywords to identify the SQL construct to transform
- IGNORE the current module's context assumptions
- DESIGN patterns that find the construct ANYWHERE in any SQL statement
- CREATE transformations that work regardless of statement type or structure
- IMPLEMENT global search-and-replace logic for maximum compatibility

CONTEXT-AGNOSTIC IMPLEMENTATION PRINCIPLES:
- NEVER assume the target construct appears in a specific SQL clause
- ALWAYS use global pattern matching across the entire statement
- FOCUS only on the specific construct mentioned in keywords
- TRANSFORM the construct while preserving all surrounding SQL code
- ENSURE the module works in any SQL context

STATEMENT-AGNOSTIC IMPLEMENTATION STRATEGY:
- BUILD transformation logic that works in ANY SQL statement context
- CREATE global patterns that find constructs regardless of location
- IMPLEMENT direct construct transformation without context dependencies
- ENSURE transformations work in various SQL statement types

MODULE ARCHITECTURE:
```python
def function_name(data, schema):
    # Find the target construct ANYWHERE in the statement
    target_pattern = r'TARGET_CONSTRUCT_PATTERN'  # Based on keywords

    # Transform ALL occurrences regardless of context
    transformed_data = re.sub(target_pattern, replacement_function, data, flags=re.IGNORECASE | re.DOTALL)

    return transformed_data
```

CONTEXT-INDEPENDENT MODULE DESIGN:
- REPLACE context-dependent logic with global construct matching
- IMPLEMENT direct pattern matching based on keywords only
- REMOVE assumptions about statement structure or SQL clause location
- CREATE transformations that work in any SQL statement type or context

COMMON CONTEXT DEPENDENCY MISTAKES TO AVOID:
- Searching only within specific statements when construct can appear anywhere
- Assuming statements end with semicolons when they might be part of larger constructs
- Looking for constructs in specific SQL clauses instead of globally
- Requiring specific statement patterns instead of focusing on the target construct
- Making assumptions about surrounding SQL context

COMPATIBILITY VALIDATION:
- TEST enhanced module against various SQL statement types
- VERIFY the module finds and transforms the target construct regardless of context
- ENSURE transformations work in nested structures, subqueries, and complex statements
- CONFIRM the module produces exact AI expected output in any SQL context

CONTEXT-INDEPENDENCE VERIFICATION:
- VALIDATE that the module works in various SQL contexts
- ENSURE patterns match constructs in any SQL clause or statement position
- CONFIRM transformations preserve surrounding SQL code correctly
- VERIFY the module handles the target construct universally across scenarios

SAFE REGEX IMPLEMENTATION:
- Use proper capturing groups when backreferences are needed
- Validate all regex patterns for syntax correctness
- Handle edge cases and malformed input gracefully
- Test patterns mentally before implementation

OUTPUT REQUIREMENTS:
===================

Provide a JSON response with PRESERVE-FIRST enhanced module:

ANALYSIS APPROACH:
- UNDERSTAND what this module handles and how it works
- IDENTIFY what variations are missing or incomplete
- ANALYZE the gap between current handling and required coverage
- DESIGN supplementary logic that enhances the module to handle variations
- ENSURE the enhanced module handles the current case AND similar scenarios

ENHANCED MODULE ARCHITECTURE:
- PRESERVE all existing module code and handling logic
- ADD supplementary transformation logic that extends coverage
- ENSURE the enhanced module handles variations
- MAINTAIN backward compatibility for existing scenarios
- VALIDATE that new variations are handled correctly

ENHANCED MODULE IMPLEMENTATION STRATEGY:
```python
def existing_function_name(input_statement, schema_name):
    # Step 1: Keep existing logic (DO NOT REPEAT if it already works)
    existing_output = existing_transformation_logic(input_statement, schema_name)

    # Step 2: ONLY add logic if existing output doesn't match AI expected content
    if existing_output_needs_enhancement():
        final_output = add_missing_transformation(existing_output)
    else:
        return existing_output  # No change needed if already correct

    return final_output
```

SELF-VALIDATION CHECKLIST:
=========================
Before finalizing the enhanced code, mentally verify:
- Does every backreference (\\1, \\2, etc.) have a corresponding capturing group ()?
- Are all special regex characters properly escaped?
- Will patterns handle ALL formatting variations (spacing, case, nesting)?
- Does the transformation produce exact AI expected output format?
- Are re.IGNORECASE and re.DOTALL flags used appropriately?
- Does the code work regardless of input formatting style?
- Are patterns flexible enough to handle real-world SQL variations?

SUCCESS CRITERIA:
================
- Enhanced module output must match AI expected CONTENT exactly
- IGNORE formatting differences (whitespace, tabs, line breaks)
- IGNORE case differences in keywords
- IGNORE table name formatting variations
- FOCUS on logical SQL content and structure match
- If existing code already produces correct content, DO NOT change anything

CONTEXT-INDEPENDENT TRANSFORMATION PRINCIPLES:
=============================================

CONSTRUCT MATCHING:
- FOCUS only on the specific SQL construct mentioned in keywords
- SEARCH for the construct ANYWHERE in the SQL statement
- AVOID assumptions about statement structure
- USE global pattern matching that works across all SQL statement types
- IMPLEMENT direct construct transformation without context dependencies

ANTI-PATTERN GUIDELINES:
- DO NOT search only within specific statements - constructs can appear anywhere
- DO NOT assume statements end with semicolons - they might be part of larger constructs
- DO NOT look for constructs in specific SQL clauses - search globally
- DO NOT require specific statement patterns - focus only on the target construct
- DO NOT make assumptions about surrounding SQL context or structure

GLOBAL TRANSFORMATION STRATEGY:
- IDENTIFY the exact construct from keywords
- CREATE patterns that match the construct regardless of its location in SQL
- IMPLEMENT transformations that work in any SQL statement context
- PRESERVE all surrounding SQL code while transforming only the target construct
- VALIDATE the transformation works universally across SQL scenarios

COMMENT MARKER PRESERVATION:
- IGNORE and PRESERVE comment markers like "comment_quad_marker_0_us", "comment_quad_marker_1_us", etc.
- DO NOT transform or modify any text matching pattern: comment_quad_marker_[digit]_us
- THESE are comment masking placeholders that must remain unchanged
- ENSURE transformations work around these markers without affecting them
- PRESERVE the exact format and position of all comment markers

TRANSFORMATION EFFECTIVENESS ANALYSIS:
=====================================

CURRENT VS EXPECTED OUTPUT ANALYSIS:
- COMPARE the current module output with the AI expected output character by character
- IDENTIFY exactly what transformation is missing or incorrect
- FOCUS on the specific SQL constructs that need to be changed
- UNDERSTAND the exact format the AI expects and ensure the module produces it

PATTERN MATCHING EFFECTIVENESS:
- IF the module shows "No transformation applied", the patterns are not matching the input
- ANALYZE the actual input format and adjust patterns to match it exactly
- ENSURE patterns are flexible enough to handle whitespace, case, and formatting variations
- TEST patterns against the real input, not just theoretical examples

REPLACEMENT LOGIC EFFECTIVENESS:
- IF patterns match but output is wrong, the replacement logic needs improvement
- ENSURE replacements produce the exact format expected by the AI
- VALIDATE that the replacement handles all parts of the matched pattern correctly
- CONFIRM the replacement produces syntactically correct and functionally equivalent output

PATTERN VALIDATION FUNDAMENTALS:
- VALIDATE all regex patterns for syntactic correctness
- ENSURE capturing groups match backreference usage
- IMPLEMENT flexible whitespace and case handling
- CREATE patterns that adapt to different formatting styles
- TEST patterns against various input format scenarios

ROBUST IMPLEMENTATION STRATEGY:
- BUILD transformation logic that is format-independent
- DESIGN patterns that capture essential SQL structure
- IMPLEMENT comprehensive matching for target constructs
- ENSURE transformations work across all input variations
- VALIDATE output consistency regardless of input formatting

WHITESPACE HANDLING:
- Use \\s* for optional whitespace (zero or more)
- Use \\s+ for required whitespace (one or more)
- Account for tabs, spaces, and newlines in SQL

TRANSFORMATION EFFECTIVENESS RULES:
==================================

DETECTION FIRST APPROACH:
- ANALYZE why the current module shows "No transformation applied"
- IDENTIFY if the issue is pattern detection or transformation logic
- IMPROVE pattern matching to actually find the target constructs in the input
- ENSURE patterns work with the actual input format, not just theoretical cases

TRANSFORMATION SECOND APPROACH:
- IF patterns are detected but transformation is wrong, fix the replacement logic
- ENSURE the replacement produces the exact AI expected output format
- VALIDATE that the transformation handles the specific input case correctly
- TEST the transformation logic against the actual input and expected output

EFFECTIVENESS VALIDATION:
- The enhanced module MUST show "Statement transformed" in the logs
- The transformation MUST produce output that matches AI expected output
- If the module still shows "No transformation applied", the enhancement failed
- Focus on making the module actually work, not just preserving existing code

EFFICIENCY RULES:
================
- DO NOT repeat existing code that already works
- ONLY add logic where there's an actual content gap
- If existing module already handles the case correctly, return as-is
- ADD minimal code necessary to bridge the content gap
- MOST IMPORTANTLY: Ensure the module actually applies transformation when needed

TRANSFORMATION DEBUGGING CHECKLIST:
===================================

BEFORE SUBMITTING THE ENHANCED MODULE, VERIFY:
1. **PATTERN DETECTION**: Will the patterns actually match the input format?
2. **TRANSFORMATION LOGIC**: Will the replacement produce the exact AI expected output?
3. **REGEX SYNTAX**: Are all regex patterns syntactically correct with proper capturing groups?
4. **EDGE CASES**: Does the module handle variations in whitespace, case, and formatting?
5. **EFFECTIVENESS**: Will this module show "Statement transformed" instead of "No transformation applied"?

COMMON FAILURE PATTERNS TO AVOID:
- Patterns that are too restrictive and don't match the actual input format
- Replacement logic that doesn't produce the exact expected output format
- Regex patterns with invalid group references or syntax errors
- Modules that preserve existing functionality but don't add the needed transformation
- Enhancements that work in theory but fail on the actual input data

{{
  "enhanced_code": "Complete enhanced Python module with syntactically correct regex patterns that produces exact AI expected output and actually applies transformation",
  "analysis": "Detailed explanation of: 1) What this module handles, 2) What transformation gap was identified, 3) What enhancement was added to bridge the gap, 4) How the enhanced module produces exact AI expected output, 5) Why this enhancement will actually apply transformation (not show 'No transformation applied'), 6) Self-validation confirmation: all regex patterns checked for syntax correctness, capturing groups verified for backreferences, edge cases considered"
}}

TRANSFORMATION SUCCESS REQUIREMENTS:
=====================================

MANDATORY OUTPUT MATCHING:
- The enhanced module MUST produce output that matches the AI expected output exactly
- If the current module produces no transformation, the enhanced module MUST apply transformation
- Focus on making the module actually transform the input to match the expected output
- The module should detect the target pattern and apply the correct transformation

PATTERN DETECTION IMPROVEMENT:
- If the module is not detecting patterns, improve the regex patterns to be more flexible
- Ensure patterns work in the actual SQL context (FOR loops, nested queries, etc.)
- Test patterns against the actual input format, not just theoretical examples
- Make patterns case-insensitive and whitespace-flexible

TRANSFORMATION LOGIC ENHANCEMENT:
- If the module detects patterns but doesn't transform correctly, fix the replacement logic
- Ensure the replacement produces the exact format expected by the AI
- Handle all variations of the target construct that appear in the input
- Validate that the transformation logic produces syntactically correct output

DEBUGGING APPROACH:
- If the module shows "No transformation applied", the detection patterns need improvement
- If the module transforms but output doesn't match AI expected, the replacement logic needs fixing
- Focus on the specific gap between current output and AI expected output
- Ensure the module handles the exact input format provided

CRITICAL SUCCESS FACTORS:
- PRESERVE all existing module functionality completely
- ADD enhancement logic only where needed
- ENSURE all regex patterns are syntactically correct (NO invalid group references)
- VALIDATE that enhanced module produces exact AI expected output
- FOCUS on the SQL construct this module is designed to handle
- HANDLE similar variations generically
- MAINTAIN backward compatibility with existing scenarios
- TRACK failures in attempt history for continuous learning
- NEVER use invalid regex group references (\\1, \\2 without capturing groups)
- MOST IMPORTANTLY: The module MUST actually apply transformation to match AI expected output
"""

    return prompt
