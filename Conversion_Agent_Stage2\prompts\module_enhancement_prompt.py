"""
Prompts for module enhancement in Stage 2 conversion analysis.
"""

def create_module_enhancement_prompt(
    original_module_code: str,
    qmigrator_target_statement: str,
    ai_corrected_statement: str,
    deployment_error: str,
    ai_comparison_feedback: str = None,
    attempt_history: dict = None,
    migration_name: str = None,
    feature_name: str = None,
    keywords: str = None
) -> str:
    """
    Create a preserve-first analysis prompt for enhancing Python modules additively.

    This prompt focuses on preserving existing functionality while adding supplementary
    logic to bridge the specific transformation gap between current and expected output.

    Args:
        original_module_code: Current working Python module (DO NOT MODIFY)
        qmigrator_target_statement: Current output from existing module
        ai_corrected_statement: Expected output that we need to achieve
        deployment_error: Error context from deployment
        ai_comparison_feedback: Feedback from AI comparison (optional)
        attempt_history: Previous enhancement attempts with their outcomes (optional)
        migration_name: Migration name for database-specific context (optional)
        feature_name: Feature name from keyword mapping (optional)
        keywords: Keywords associated with this feature (optional)

    Returns:
        Formatted prompt string for additive module enhancement
    """

    # Build feedback section
    feedback_section = ""
    if ai_comparison_feedback:
        feedback_section = f"\nAI COMPARISON FEEDBACK:\n{ai_comparison_feedback}\n"

    # Build hybrid attempt history section for efficient learning
    attempt_history_section = ""
    if attempt_history and isinstance(attempt_history, dict) and (attempt_history.get('recent_attempts') or attempt_history.get('failure_patterns')):
        attempt_history_section = "\nATTEMPT HISTORY LEARNING (HYBRID APPROACH):\n"
        attempt_history_section += "=" * 70 + "\n"

        # Show all detailed attempts (no summarization)
        all_attempts = attempt_history.get('all_attempts', [])
        if all_attempts:
            attempt_history_section += f"\nALL PREVIOUS ATTEMPTS ({len(all_attempts)} total):\n"
            for attempt in all_attempts:
                attempt_num = attempt.get('attempt_number', 'Unknown')
                modules_used = attempt.get('modules_used', [])
                ai_feedback = attempt.get('ai_feedback', 'No feedback available')

                attempt_history_section += f"\nAttempt {attempt_num} - {len(modules_used)} modules:\n"

                # Safely extract module names with error handling
                module_names = []
                for m in modules_used:
                    if isinstance(m, dict):
                        module_name = m.get('module_name', 'Unknown')
                        if isinstance(module_name, str):
                            module_names.append(module_name)
                        else:
                            module_names.append(str(module_name))
                    else:
                        module_names.append(str(m))

                attempt_history_section += f"  Modules: {', '.join(module_names)}\n"
                attempt_history_section += f"  Why it failed: {ai_feedback[:200]}...\n"

        total_attempts = attempt_history.get('total_attempts', 0)
        attempt_history_section += f"\nTotal attempts analyzed: {total_attempts}\n"

        attempt_history_section += "\nSMART LEARNING INSTRUCTIONS:\n"
        attempt_history_section += "- ANALYZE what supplementary approaches were tried and why they failed\n"
        attempt_history_section += "- IDENTIFY patterns in failed additive logic attempts\n"
        attempt_history_section += "- TRY fundamentally different supplementary transformation strategies\n"
        attempt_history_section += "- AVOID repeating the same additive logic patterns that already failed\n"
        attempt_history_section += "- FOCUS on different gap-bridging approaches not yet attempted\n"
        attempt_history_section += "- LEARN from failed supplementary logic to design better additive solutions\n"
        attempt_history_section += "=" * 70 + "\n"

    # Build feature context section
    feature_context_section = ""
    if feature_name or keywords or migration_name:
        feature_context_section = f"""
FEATURE CONTEXT FROM KEYWORD MAPPING:
====================================
"""
        if migration_name:
            feature_context_section += f"Migration: {migration_name}\n"
        if feature_name:
            feature_context_section += f"Feature Name: {feature_name}\n"
        if keywords:
            feature_context_section += f"Associated Keywords: {keywords}\n"
        feature_context_section += "\n"

    prompt = f"""
You are an expert Python developer specializing in SQL feature transformation. Your task is to enhance a specific feature module to produce the exact AI expected output.

FEATURE-SPECIFIC ENHANCEMENT APPROACH:
=====================================

CURRENT FEATURE MODULE (ANALYZE CAREFULLY):
{original_module_code}

TRANSFORMATION REQUIREMENT:
==========================

CURRENT OUTPUT:
{qmigrator_target_statement}

REQUIRED OUTPUT (EXACT MATCH NEEDED):
{ai_corrected_statement}

DEPLOYMENT ERROR CONTEXT:
{deployment_error}

{feedback_section}
{attempt_history_section}
{feature_context_section}

FEATURE ANALYSIS METHODOLOGY:
=============================

1. FEATURE IDENTIFICATION:
   - ANALYZE the module code to identify what SQL feature it handles (e.g., XML functions, JOIN operations, function conversions, etc.)
   - UNDERSTAND the current transformation patterns used in the module
   - RECOGNIZE what variations are already supported by existing logic
   - EXAMINE the existing regex patterns and transformation logic

2. OUTPUT GAP ANALYSIS:
   - COMPARE current output vs required output for THIS SPECIFIC FEATURE
   - IDENTIFY what transformation is missing for this feature
   - FOCUS on the feature-specific logic gap, not general formatting
   - DETERMINE what additional patterns are needed for this feature

3. FEATURE ENHANCEMENT STRATEGY:
   - PRESERVE all existing feature transformation logic
   - ADD supplementary logic to handle the missing feature variation
   - ENSURE new patterns work for similar variations of the same feature
   - MAINTAIN backward compatibility for existing feature scenarios

4. ENHANCED MODULE CONSTRUCTION:
   - PRESERVE all existing module code and functionality completely
   - ADD supplementary transformation function after existing logic
   - ENSURE final output matches AI expected output format exactly
   - MAINTAIN backward compatibility and existing functionality

5. VALIDATION APPROACH:
   - VERIFY enhanced module produces exact AI expected output
   - ENSURE existing functionality remains intact
   - VALIDATE that supplementary logic handles the gap correctly
   - CONFIRM backward compatibility with all scenarios

SUPPLEMENTARY LOGIC GUIDELINES:
===============================

UNDERSTAND-THEN-ENHANCE APPROACH:
- ANALYZE current module: understand what it does and how it works
- IDENTIFY transformation gap: what's missing to get AI expected output
- DESIGN supplementary logic: additive transformation to bridge the gap
- ENSURE final output: enhanced module produces exact AI expected format

FEATURE-CENTRIC GAP-BRIDGING:
- Take current module output as input to supplementary logic
- Apply feature-specific transformation to handle missing variations
- Use pattern-based approach that covers ALL scenarios of this feature
- Focus on enhancing the feature's complete transformation capability

PYTHON CODING REQUIREMENTS:
===========================

REGEX PATTERN SAFETY:
- ALWAYS validate regex patterns before using them
- NEVER use backreferences (\1, \2) without corresponding capturing groups ()
- USE raw strings r"pattern" for all regex patterns
- TEST regex patterns mentally before implementation
- ESCAPE special characters properly with re.escape() when needed

VALID REGEX EXAMPLES:
- Capturing group: r'(pattern)' with replacement r'\\1'
- Non-capturing group: r'(?:pattern)'
- Safe replacement: r'new_pattern' (no backreferences)
- Escaped literals: re.escape(literal_string)
- Case-insensitive: re.sub(pattern, replacement, text, flags=re.IGNORECASE)

INVALID REGEX PATTERNS (AVOID):
- r'pattern' with replacement r'\\1' (no capturing group)
- Unescaped special characters in patterns
- Malformed group references
- Missing re.IGNORECASE flag for case-insensitive matching
- Not handling optional whitespace: use \\s* or \\s+ appropriately

COMMON MISTAKES TO AVOID:
- Using \\1 without capturing group: r'pattern' -> r'\\1' ❌
- Correct approach: r'(pattern)' -> r'\\1' ✅
- Forgetting to escape special characters like . + * ? [ ] ( ) {{}} | \\
- Not accounting for optional whitespace between SQL keywords
- Missing flags for case-insensitive and multiline matching

ADDITIVE ENHANCEMENT PRINCIPLES:
- Preserve existing regex patterns and logic
- Keep all existing code unchanged
- Add new logic after existing logic
- Apply new transformations only if existing logic doesn't transform
- Maintain backward compatibility

IMPLEMENTATION GUIDELINES:
- Copy all existing code exactly as written
- Add new logic after existing code
- Add new patterns that work for the current failing case
- Ensure the module actually transforms the input (not "No transformation applied")
- Focus on achieving the target output format

FEATURE-SPECIFIC TRANSFORMATION RULES:
=====================================

UNIVERSAL PATTERN MATCHING PRINCIPLES:
=====================================

FORMATTING-AGNOSTIC APPROACH:
- DESIGN patterns that work regardless of whitespace variations
- HANDLE all possible spacing: spaces, tabs, newlines, mixed combinations
- SUPPORT all case variations: uppercase, lowercase, mixed case
- ACCOUNT for optional elements and different ordering
- PROCESS nested structures with varying complexity levels

ROBUST REGEX CONSTRUCTION GUIDELINES:
- USE flexible whitespace patterns (\\s+ for required, \\s* for optional)
- APPLY case-insensitive matching for all SQL keywords
- IMPLEMENT balanced parentheses matching for nested structures
- HANDLE quoted identifiers and string literals appropriately
- SUPPORT schema-qualified names and aliases

ADAPTIVE PATTERN STRATEGY:
- ANALYZE the feature keywords to understand what constructs to match
- BUILD patterns that capture the essential structure, not specific formatting
- CREATE flexible capturing groups for transformation components
- ENSURE patterns work across different SQL dialects and styles
- VALIDATE patterns against multiple formatting variations

CONTEXT-INDEPENDENT TRANSFORMATION STRATEGY:
- SEARCH for target constructs ANYWHERE in the statement (not just specific contexts)
- AVOID assumptions about statement structure (SELECT vs FOR vs INSERT vs UPDATE)
- FOCUS on the specific SQL construct mentioned in feature keywords
- TRANSFORM the construct regardless of its surrounding context
- PRESERVE all surrounding code while transforming only the target construct

UNIVERSAL PATTERN MATCHING APPROACH:
- USE global search patterns that work across all SQL statement types
- AVOID context-specific assumptions (like "must be in SELECT" or "must end with ;")
- IMPLEMENT construct-specific matching based on feature keywords only
- HANDLE nested constructs within any SQL statement structure
- ENSURE patterns work in FOR loops, subqueries, CTEs, functions, etc.

UNIVERSAL TRANSFORMATION METHODOLOGY:
===================================

UNIVERSAL ENHANCEMENT METHODOLOGY:
- ANALYZE feature keywords to identify the EXACT SQL construct to transform
- IGNORE the current module's context assumptions (SELECT-only, etc.)
- DESIGN patterns that find the construct ANYWHERE in any SQL statement
- CREATE transformations that work regardless of statement type or structure
- IMPLEMENT global search-and-replace logic for maximum compatibility

CONTEXT-AGNOSTIC IMPLEMENTATION PRINCIPLES:
- NEVER assume the target construct appears in a specific SQL clause
- ALWAYS use global pattern matching across the entire statement
- FOCUS only on the specific construct mentioned in feature keywords
- TRANSFORM the construct while preserving all surrounding SQL code
- ENSURE the module works in any SQL context (loops, subqueries, functions, etc.)

STATEMENT-AGNOSTIC IMPLEMENTATION STRATEGY:
- BUILD transformation logic that works in ANY SQL statement context
- CREATE global patterns that find constructs regardless of location
- IMPLEMENT direct construct transformation without context dependencies
- ENSURE transformations work in FOR loops, SELECT, INSERT, UPDATE, functions, etc.

UNIVERSAL MODULE ARCHITECTURE:
```python
def feature_function(data, schema):
    # WRONG: Context-specific approach
    # select_patterns = re.findall(r'SELECT.*?;', data)  # Only works for SELECT statements

    # CORRECT: Global construct-specific approach
    # Find the target construct ANYWHERE in the statement
    target_pattern = r'TARGET_CONSTRUCT_PATTERN'  # Based on feature keywords

    # Transform ALL occurrences regardless of context
    transformed_data = re.sub(target_pattern, replacement_function, data, flags=re.IGNORECASE | re.DOTALL)

    return transformed_data
```

CONTEXT-INDEPENDENT MODULE DESIGN:
- REPLACE context-dependent logic with global construct matching
- IMPLEMENT direct pattern matching based on feature keywords only
- REMOVE assumptions about statement structure or SQL clause location
- CREATE transformations that work in any SQL statement type or context

COMMON CONTEXT DEPENDENCY MISTAKES TO AVOID:
- Searching only within SELECT statements when construct can appear anywhere
- Assuming statements end with semicolons when they might be part of larger constructs
- Looking for constructs in specific SQL clauses instead of globally
- Requiring specific statement patterns instead of focusing on the target construct
- Making assumptions about surrounding SQL context

UNIVERSAL COMPATIBILITY VALIDATION:
- TEST enhanced module against ALL SQL statement types (SELECT, FOR, INSERT, UPDATE, etc.)
- VERIFY the module finds and transforms the target construct regardless of context
- ENSURE transformations work in nested structures, subqueries, and complex statements
- CONFIRM the module produces exact AI expected output in any SQL context

CONTEXT-INDEPENDENCE VERIFICATION:
- VALIDATE that the module works in FOR loops, not just SELECT statements
- ENSURE patterns match constructs in any SQL clause or statement position
- CONFIRM transformations preserve surrounding SQL code correctly
- VERIFY the module handles the target construct universally across all scenarios

SAFE REGEX IMPLEMENTATION:
- Use proper capturing groups when backreferences are needed
- Validate all regex patterns for syntax correctness
- Handle edge cases and malformed input gracefully
- Test patterns mentally before implementation

OUTPUT REQUIREMENTS:
===================

Provide a JSON response with PRESERVE-FIRST enhanced module:

FEATURE-CENTRIC ANALYSIS APPROACH:
- UNDERSTAND what specific feature this module handles and how it works
- IDENTIFY what variations of this feature are missing or incomplete
- ANALYZE the gap between current feature handling and required feature coverage
- DESIGN supplementary logic that enhances the feature to handle ALL variations
- ENSURE the enhanced module handles the current case AND similar feature scenarios

ENHANCED FEATURE MODULE ARCHITECTURE:
- PRESERVE all existing module code and feature handling logic
- ADD supplementary transformation logic that extends feature coverage
- ENSURE the enhanced module handles ALL variations of the specific feature
- MAINTAIN backward compatibility for all existing feature scenarios
- VALIDATE that new feature variations are handled correctly

ENHANCED MODULE IMPLEMENTATION STRATEGY:
```python
def existing_function_name(input_statement, schema_name):
    # Step 1: Keep existing logic (DO NOT REPEAT if it already works)
    existing_output = existing_transformation_logic(input_statement, schema_name)

    # Step 2: ONLY add logic if existing output doesn't match AI expected content
    if existing_output_needs_enhancement():
        final_output = add_missing_transformation(existing_output)
    else:
        return existing_output  # No change needed if already correct

    return final_output
```

SELF-VALIDATION CHECKLIST:
=========================
Before finalizing the enhanced code, mentally verify:
- Does every backreference (\\1, \\2, etc.) have a corresponding capturing group ()?
- Are all special regex characters properly escaped?
- Will patterns handle ALL formatting variations (spacing, case, nesting)?
- Does the transformation produce exact AI expected output format?
- Are re.IGNORECASE and re.DOTALL flags used appropriately?
- Does the code work regardless of input formatting style?
- Are patterns flexible enough to handle real-world SQL variations?

SUCCESS CRITERIA:
================
- Enhanced module output must match AI expected CONTENT exactly
- IGNORE formatting differences (whitespace, tabs, line breaks)
- IGNORE case differences in keywords
- IGNORE table name formatting variations
- FOCUS on logical SQL content and structure match
- If existing code already produces correct content, DO NOT change anything

CONTEXT-INDEPENDENT TRANSFORMATION PRINCIPLES:
=============================================

UNIVERSAL CONSTRUCT MATCHING:
- FOCUS only on the specific SQL construct mentioned in feature keywords
- SEARCH for the construct ANYWHERE in the SQL statement, not just specific contexts
- AVOID assumptions about statement structure (SELECT vs FOR vs INSERT vs UPDATE)
- USE global pattern matching that works across all SQL statement types
- IMPLEMENT direct construct transformation without context dependencies

ANTI-PATTERN GUIDELINES:
- DO NOT search only within SELECT statements - constructs can appear anywhere
- DO NOT assume statements end with semicolons - they might be part of larger constructs
- DO NOT look for constructs in specific SQL clauses - search globally
- DO NOT require specific statement patterns - focus only on the target construct
- DO NOT make assumptions about surrounding SQL context or structure

GLOBAL TRANSFORMATION STRATEGY:
- IDENTIFY the exact construct from feature keywords (e.g., "TABLE", "XMLSEQUENCE")
- CREATE patterns that match the construct regardless of its location in SQL
- IMPLEMENT transformations that work in any SQL statement context
- PRESERVE all surrounding SQL code while transforming only the target construct
- VALIDATE the transformation works universally across all SQL scenarios

ADDITIVE PATTERN DETECTION STRATEGY:
===================================

CURRENT CASE ANALYSIS:
- EXISTING PATTERNS: May be too restrictive for current input format
- MISSING DETECTION: Patterns don't match the actual SQL structure
- ADDITIVE SOLUTION: Add broader patterns that catch missed cases

ADDITIVE PATTERN APPROACH:
```python
def enhanced_function(data, schema):
    original_data = data

    # PRESERVE: Keep all existing logic exactly as-is
    # ... existing code unchanged ...

    # ADD: Supplementary detection for missed cases
    if data == original_data:  # Existing logic didn't transform
        # Add broader patterns to catch the current case
        broader_pattern = r'BROADER_PATTERN_FOR_CURRENT_CASE'
        if re.search(broader_pattern, data, re.IGNORECASE | re.DOTALL):
            # Apply transformation to achieve AI corrected output
            transformed_data = apply_transformation_for_ai_output(data)
            return transformed_data

    return data
```

PATTERN ENHANCEMENT GUIDELINES:
- ADD patterns that work in FOR loops, not just SELECT statements
- ADD patterns that don't require semicolon endings
- ADD patterns that find constructs ANYWHERE in the statement
- PRESERVE all existing patterns and logic

COMMENT MARKER PRESERVATION:
- IGNORE and PRESERVE comment markers like "comment_quad_marker_0_us", "comment_quad_marker_1_us", etc.
- DO NOT transform or modify any text matching pattern: comment_quad_marker_[digit]_us
- THESE are comment masking placeholders that must remain unchanged
- ENSURE transformations work around these markers without affecting them
- PRESERVE the exact format and position of all comment markers

PATTERN VALIDATION FUNDAMENTALS:
- VALIDATE all regex patterns for syntactic correctness
- ENSURE capturing groups match backreference usage
- IMPLEMENT flexible whitespace and case handling
- CREATE patterns that adapt to different formatting styles
- TEST patterns against various input format scenarios

ROBUST IMPLEMENTATION STRATEGY:
- BUILD transformation logic that is format-independent
- DESIGN patterns that capture essential SQL structure
- IMPLEMENT comprehensive matching for target constructs
- ENSURE transformations work across all input variations
- VALIDATE output consistency regardless of input formatting

WHITESPACE HANDLING:
- Use \\s* for optional whitespace (zero or more)
- Use \\s+ for required whitespace (one or more)
- Account for tabs, spaces, and newlines in SQL

EFFICIENCY RULES:
================
- DO NOT repeat existing code that already works
- ONLY add logic where there's an actual content gap
- If existing module already handles the case correctly, return as-is
- ADD minimal code necessary to bridge the content gap

{{
  "enhanced_code": "Complete enhanced Python module with syntactically correct regex patterns that produces exact AI expected output for the specific SQL feature",
  "analysis": "Detailed explanation of: 1) What specific SQL feature this module handles (e.g., XML functions, JOIN operations, function conversions), 2) What transformation gap was identified, 3) What feature-specific enhancement was added to bridge the gap, 4) How the enhanced module produces exact AI expected output, 5) Self-validation confirmation: all regex patterns checked for syntax correctness, capturing groups verified for backreferences, edge cases considered"
}}

CRITICAL SUCCESS FACTORS:
- PRESERVE all existing module functionality completely
- ADD feature-specific enhancement logic only where needed
- ENSURE all regex patterns are syntactically correct (NO invalid group references)
- VALIDATE that enhanced module produces exact AI expected output
- FOCUS on the specific SQL feature this module is designed to handle (XML, JOIN, function conversion, etc.)
- HANDLE similar variations of the same SQL feature generically (e.g., different XML functions, various JOIN types)
- MAINTAIN backward compatibility with existing scenarios
- TRACK failures in attempt history for continuous learning
- NEVER use invalid regex group references (\\1, \\2 without capturing groups)

ENHANCEMENT GOAL - ACHIEVE TARGET OUTPUT:
========================================

TRANSFORMATION TARGET:
- The enhanced module should transform current output into AI corrected output exactly
- Focus on the specific SQL constructs that need to change
- Ignore formatting differences (whitespace, case, brackets)
- Ensure the module shows "Statement transformed" not "No transformation applied"

SUCCESS CRITERIA:
- Preserve all existing functionality
- Add new logic to handle the current failing case
- Achieve final output that matches AI corrected format exactly
- Ensure module actually applies transformation to the input

{{
  "enhanced_code": "Complete enhanced Python module using additive approach that preserves existing functionality and adds new logic to achieve AI corrected output exactly",
  "analysis": "Detailed explanation of: 1) What existing functionality was preserved, 2) What new additive logic was added, 3) How the enhanced module achieves AI corrected output, 4) Why this will show 'Statement transformed' instead of 'No transformation applied', 5) Confirmation that all existing patterns and logic remain unchanged"
}}
"""

    return prompt
