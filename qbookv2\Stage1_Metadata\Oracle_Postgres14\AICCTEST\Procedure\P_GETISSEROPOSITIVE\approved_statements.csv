migration_name,schema_name,object_name,object_type,tgt_object_id,source_statement_number,target_statement_number,original_source_statement,original_target_statement,ai_converted_statement,original_deployment_error
Oracle_Postgres14,AICCTEST,P_GETISSEROPOSITIVE,Procedure,194418,12,14,"FOR I IN (SELECT EXTRACTVALUE(XT.COLUMN_VALUE, 'E') AS SEROTESTIDS
                FROM TABLE(XMLSEQUENCE(EXTRACT(XMLTYPE(REPLACE(('<X><E>' ||
                                                               (SELECT BCNF.CONFIGVALUE
                                                                   FROM BB.BBCONFIG BCNF
                                                                  WHERE UPPER(BCNF.CONFIG_KEY) =
                                                                        'ISSEROPOSITIVETESTS') ||
                                                               '</E></X>'),
                                                               ',',
                                                               '</E><E>')),
                                               '/X/E'))) XT) LOOP

      EXIT WHEN I.SEROTESTIDS IS NULL;","FOR I IN (
            SELECT
                (
                    CASE WHEN (
                        SELECT
                            unnest(xpath('E', XT.COLUMN_VALUE)))::text = '' THEN
                        NULL
                    ELSE
                        (
                            SELECT
                                unnest(xpath('E', XT.COLUMN_VALUE)))::text
                    END) AS SEROTESTIDS
            FROM
                TABLE (XMLSEQUENCE (extract(xml(REPLACE(('<X><E>' || (
                                        SELECT
                                            BCNF.CONFIGVALUE
                                        FROM BB.BBCONFIG BCNF
                                    WHERE
                                        UPPER(BCNF.CONFIG_KEY) = 'ISSEROPOSITIVETESTS') || '</E></X>'), ',', '</E><E>')), '/X/E'))) XT)
            LOOP
                EXIT
    WHEN I.SEROTESTIDS IS NULL;","FOR i IN (
SELECT(SELECT UNNEST (xpath ( 'E',xt.column_value ) ) ) AS serotestids
FROM (WITH ctc AS (SELECT UNNEST (xpath ('/X/E',XMl (REPLACE ( ('<X><E>' || 
(SELECT BCNF.CONFIGVALUE FROM BB.BBCONFIG BCNF WHERE UPPER (BCNF.CONFIG_KEY) = 'ISSEROPOSITIVETESTS') ||
 '</E></X>'),',','</E><E>')))) AS column_value) SELECT column_value FROM ctc )xt) 
LOOP 
EXIT WHEN i.serotestids IS NULL ; ","Postgres14 Error: ERROR:  syntax error at or near ""TABLE""
LINE 60:                 TABLE (XMLSEQUENCE (extract(xml(REPLACE(('<X...
                         ^
"
