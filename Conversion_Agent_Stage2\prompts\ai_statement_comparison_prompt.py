"""
AI Statement Comparison Prompt for Stage 2 Processing.

This module creates prompts for AI-driven comparison of AI corrected output vs applied modules output
to determine if they have similar target functionality.
"""

def create_ai_statement_comparison_prompt(
    ai_corrected_statement: str,
    applied_modules_statement: str,
    db_terms: dict
) -> str:
    """
    Create a simplified prompt for AI-driven statement comparison.

    Args:
        ai_corrected_statement: AI corrected PostgreSQL statement from Stage 1
        applied_modules_statement: Statement after applying updated modules
        db_terms: Database-specific terminology

    Returns:
        str: Simplified comparison prompt for AI analysis
    """

    target_db = db_terms['target_db']
    expert_title = db_terms['expert_title']

    prompt = f"""
You are a SENIOR {expert_title} with expertise in {target_db} statement comparison.

Your task is to compare two {target_db} statements to determine if they are essentially the same with only minor formatting differences.

CORE COMPARISON TASK:
Since both statements are already in {target_db} format, focus on:
1. **STATEMENT SIMILARITY**: Determine if both statements are essentially the same {target_db} code
2. **SYNTAX MATCHING**: Check if the core SQL logic and structure are the same
3. **FORMATTING TOLERANCE**: Allow for minor differences in whitespace, case, and formatting
4. **LOGIC EQUIVALENCE**: Verify both statements implement the same database operations

COMPARISON CONTEXT:
==================
**Target Database**: {target_db}

**AI Corrected {target_db} Statement** (Expected):
{ai_corrected_statement}

**Applied Modules {target_db} Statement** (Actual):
{applied_modules_statement}

COMPARISON CRITERIA:
===================

1. **CORE LOGIC MATCHING**:
   - Do both statements implement the same SQL operations and logic?
   - Are the main database functions and operations equivalent?
   - Do both statements process the same data in the same way?

2. **ACCEPTABLE DIFFERENCES**:
   - Whitespace and formatting differences (spaces, tabs, line breaks)
   - Case differences (uppercase vs lowercase)
   - Minor syntax variations that don't change functionality
   - Variable name differences if the logic is the same

3. **UNACCEPTABLE DIFFERENCES**:
   - Different SQL functions or operations
   - Different data processing logic
   - Missing or extra functionality
   - Different result sets or outputs

COMPARISON METHODOLOGY:
======================

**STEP 1: NORMALIZE AND COMPARE**
- Remove extra whitespace and normalize formatting from both statements
- Compare the core SQL structure and operations
- Identify the main functional components of each statement

**STEP 2: ASSESS EQUIVALENCE**
- Determine if both statements perform the same database operations
- Check if the logic flow and data processing are equivalent
- Allow for minor syntax and formatting differences

**STEP 3: DECISION**
- If core logic is the same: statements match
- If core logic differs significantly: statements don't match
- Focus on functional equivalence, not exact text matching

EXAMPLE OF EQUIVALENT STATEMENTS:
================================
These should be considered EQUIVALENT:

Statement 1: `FOR i IN (SELECT unnest(xpath('E', value)) AS data FROM table)`
Statement 2: `FOR I IN(SELECT unnest(xpath('E',value))AS data FROM table)`

Differences: case (i vs I), spacing, but same core logic.

These should be considered DIFFERENT:

Statement 1: `FOR i IN (SELECT unnest(xpath('E', value)) AS data FROM table)`
Statement 2: `FOR i IN (SELECT extract(xmlparse(content value), '//E') AS data FROM table)`

Differences: completely different XML processing approach.

DECISION CRITERIA:
=================

**STATEMENTS MATCH** (return true):
✅ Both statements implement the same SQL logic and operations
✅ Core functionality is equivalent (minor syntax differences OK)
✅ Same data processing approach and result structure
✅ Formatting, whitespace, and case differences are acceptable
✅ Variable naming differences are acceptable if logic is the same

**STATEMENTS DON'T MATCH** (return false):
❌ Fundamentally different SQL operations or logic
❌ Missing or extra functionality between the statements
❌ Different data processing approaches that would yield different results
❌ Incompatible SQL syntax or structure

OUTPUT FORMAT (JSON):
====================
{{
  "statements_match": <boolean - true if both statements are essentially the same with only minor formatting differences, false if they have significant functional differences>,
  "explanation": "<brief analysis of the comparison. If statements match, explain why they are equivalent. If they don't match, explain the key functional differences that need to be addressed>"
}}

COMPARISON FOCUS:
================
- **Core Logic**: Both statements should implement the same SQL operations
- **Syntax Tolerance**: Minor formatting and case differences are acceptable
- **Functional Equivalence**: Focus on whether both statements would produce the same results
- **Simplicity**: Since both are {target_db} statements, they should be very similar

Remember: Both statements are already in {target_db} format, so they should be essentially the same with only minor differences in formatting, whitespace, or case. Focus on core SQL logic equivalence rather than exact text matching.
"""

    return prompt
