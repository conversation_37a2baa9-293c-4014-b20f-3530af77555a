import re

def extractvalue(data, schema):
    print(f"🔧 extractvalue: Input contains TABLE(XMLSEQUENCE: {'TABLE(XMLSEQUENCE' in data}")
    print(f"🔧 extractvalue: Input contains TABLE (XMLSEQUENCE: {'TABLE (XMLSEQUENCE' in data}")
    original_data = data
    # Existing logic
    data = re.sub(r' +',' ',data)
    data = re.sub(r'EXTRACT', 'extract', data, flags=re.IGNORECASE | re.DOTALL)
    data = re.sub(r'EXTRACTVALUE', 'extract', data, flags=re.IGNORECASE | re.DOTALL)
    data = re.sub('extract\s+\(', 'extract(', data, flags=re.IGNORECASE | re.DOTALL)
    data = re.sub(',\s+', ',', data)
    extract_dict = {}
    count = 0
    if re.search(r'extract', data, re.IGNORECASE):
        x = 'extract'
        startIndex = [m.start() for m in re.finditer(rf'\b{x}\s*\(.*?\)', data, flags=re.IGNORECASE | re.DOTALL)]
        for index in startIndex:
            current = []
            bracket_level = 0
            for s in data[index + len(x) - 0:]:
                current.append(s)
                if s == '(':
                    bracket_level += 1
                elif s == ')':
                    bracket_level -= 1
                    if bracket_level == 0:
                        break
            first_dateadd_str = x + ''.join(current)
            remaining_statement = first_dateadd_str
            extract_dict['key' + str(count)] = remaining_statement
            count += 1
    for key, value in extract_dict.items():
        if value.count(',') == 1:
            split_comma = value.split(',')
            before_comma = split_comma[0]
            before_comma_value = re.sub(r'extract\(', '', before_comma, flags=re.IGNORECASE)
            after_comma = split_comma[1]
            after_comma_value = re.sub(r'\)', '', after_comma, flags=re.IGNORECASE)
            forming_line = f'\n(select unnest(xpath({after_comma_value},{before_comma_value.replace("xmltype", "xml")})))'
            data = data.replace(value, forming_line)
            data = re.sub('\.\s*getstringval\s*\(\s*\)', '::text', data, flags=re.IGNORECASE | re.DOTALL)
        elif value.count(',') == 2:
            split_comma = value.split('),')
            before_comma = split_comma[0]
            before_comma_value = re.sub(r'extract\(', '', before_comma, flags=re.IGNORECASE)
            after_comma = split_comma[1]
            after_comma_value = re.sub(r'\)', '', after_comma, flags=re.IGNORECASE)
            forming_line = f'\n(select unnest(xpath({after_comma_value},{before_comma_value.replace("xmltype", "xml")}))))'
            data = data.replace(value, forming_line)
            data = re.sub('\.\s*getstringval\s*\(\s*\)', '::text', data, flags=re.IGNORECASE | re.DOTALL)
        else:
            dot_value = re.findall(
                rf'(?:\w+\s*\(\s*\w+\s*\.\s*\w+\s*\)|\w+\s*\(\s*\w+\s*\)|\w+)\s*\.\s*{re.escape(value)}',
                data,
                flags=re.DOTALL | re.IGNORECASE
            )
            for dot_before_value in dot_value:
                dot_value_first = re.findall(
                    rf'((?:\w+\s*\(\s*\w+\s*\.\s*\w+\s*\)|\w+\s*\(\s*\w+\s*\)|\w+))\s*\.\s*{re.escape(value)}',
                    data,
                    flags=re.DOTALL | re.IGNORECASE
                )
                if dot_value_first:
                    value = re.sub(r'extract\(', '', value, flags=re.IGNORECASE | re.DOTALL).strip().rstrip(')')
                    forming_line = f'(select unnest(xpath({value.replace("xmltype", "xml").replace(".", "")},{dot_value_first[0]})))'
                    data = data.replace(dot_before_value, forming_line)
            data = re.sub('\.\s*getnumberval\s*\(\s*\)', '::numeric ', data, flags=re.IGNORECASE | re.DOTALL)
            data = re.sub('\.\s*getstringval\s*\(\s*\)', '::text ', data, flags=re.IGNORECASE | re.DOTALL)
            data = re.sub(r'\bselect\b\s*\bselect\b', 'select', data, flags=re.DOTALL | re.IGNORECASE)
    # Additional logic to handle specific transformation requirements
    if "TABLE (XMLSEQUENCE" in data:
        data = re.sub(r'TABLE\s*\(\s*XMLSEQUENCE', 'WITH ctc AS (SELECT UNNEST (xpath', data, flags=re.IGNORECASE)
        data = re.sub(r'\)\s*XT\)', ') AS column_value) SELECT column_value FROM ctc )xt', data, flags=re.IGNORECASE)
    return data