Statement_Number,Statement_After_Typecasting,Statement_Level_Output,Available_Features,Post_Features
1,"CREATE OR REPLACE PROCEDURE ""AICCTEST"".""P_UPDATELOCATIONID""
(IN_FROMVAL numeric,
IN_TOVAL numeric)
as","CREATE OR REPLACE PROCEDURE ""AICCTEST"".""P_UPDATELOCATIONID""
(IN_FROMVAL numeric,
IN_TOVAL numeric)
as",[],"[('cursor', 'Procedure/Post/cursor.py'), ('record', 'Procedure/Post/record.py'), ('underscorecursor', 'Common/Post/underscorecursor.py')]"
2,"as
CURSOR C1
is
SELECT UTC.TABLE_NAME,UTC.COLUMN_NAME FROM AICCTEST.USER_TAB_COLS UTC
WHERE UTC.COLUMN_NAME LIKE 'LOCATIONID'
AND UTC.TABLE_NAME NOT IN(SELECT UM.MVIEW_NAME FROM AICCTEST.USER_MVIEWS UM);","as
CURSOR C1
is
SELECT UTC.TABLE_NAME,UTC.COLUMN_NAME FROM AICCTEST.USER_TAB_COLS UTC
WHERE UTC.COLUMN_NAME LIKE 'LOCATIONID'
AND UTC.TABLE_NAME NOT IN(SELECT UM.MVIEW_NAME FROM AICCTEST.USER_MVIEWS UM);","[('join', 'Common/Statement/Pre/join.py')]","[('cursor', 'Procedure/Post/cursor.py'), ('record', 'Procedure/Post/record.py'), ('underscorecursor', 'Common/Post/underscorecursor.py')]"
3,"comment_quad_marker_0_us
BEGIN
FOR REC IN C1
LOOP
Raise Notice 'UPDATE % SET % = % WHERE % = %;', REC.TABLE_NAME, REC.COLUMN_NAME, IN_TOVAL, REC.COLUMN_NAME, IN_FROMVAL;","comment_quad_marker_0_us
BEGIN
FOR REC IN C1
LOOP
Raise Notice 'UPDATE % SET % = % WHERE % = %;', REC.TABLE_NAME, REC.COLUMN_NAME, IN_TOVAL, REC.COLUMN_NAME, IN_FROMVAL;","[('update_alias', 'Common/Statement/Pre/update_alias.py'), ('raisenotice', 'Common/Statement/Pre/raisenotice.py')]","[('cursor', 'Procedure/Post/cursor.py'), ('record', 'Procedure/Post/record.py'), ('underscorecursor', 'Common/Post/underscorecursor.py')]"
4,END LOOP;,END LOOP;,[],"[('cursor', 'Procedure/Post/cursor.py'), ('record', 'Procedure/Post/record.py'), ('underscorecursor', 'Common/Post/underscorecursor.py')]"
5,"comment_quad_marker_1_us
END;","comment_quad_marker_1_us
END;",[],"[('cursor', 'Procedure/Post/cursor.py'), ('record', 'Procedure/Post/record.py'), ('underscorecursor', 'Common/Post/underscorecursor.py')]"
