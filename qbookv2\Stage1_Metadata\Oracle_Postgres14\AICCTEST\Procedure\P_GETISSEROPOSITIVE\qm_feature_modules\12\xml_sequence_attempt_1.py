import re

def xml_sequence(data, schema):
    # Extract SELECT statements
    select_pattern = re.findall(r'\bSELECT\b.*?;', data, flags=re.IGNORECASE | re.DOTALL)
    for select_query in select_pattern:
        # Check for TABLE(XMLSEQUENCE(...)) pattern
        check = re.search(r'\s*TABLE\s*\(\s*XMLSEQUENCE\s*\(', select_query, flags=re.DOTALL | re.I)
        if check:
            # Extract TABLE(...) statements
            from_table = re.findall(r'\s*TABLE\s*\(.*?;', select_query, flags=re.DOTALL | re.I)
            for from_tab in from_table:
                # Transform TABLE(XMLSEQUENCE(...)) to WITH CTE AS (...)
                from_manp = re.sub(r'\bTABLE\s*\(\s*XMLSEQUENCE\s*\(', '(WITH CTE AS ', from_tab, flags=re.DOTALL | re.I)
                from_manp = re.sub(r'(?:\:\s*\:\s*TEXT|:\:\s*\:\s*NUMERIC)', '', from_manp, flags=re.DOTALL | re.I)
                # Check for nested constructs
                check_wrd = re.search(r'\,\s*\w+\s*\(', from_manp, flags=re.DOTALL | re.I)
                if not check_wrd:
                    xml_stmt = re.findall(r'\(\s*SELECT\s*UNNEST\s*\(\s*XPATH\s*\(.*?\)\s*\)\s*\)', from_manp, flags=re.DOTALL | re.I)
                    for xml in xml_stmt:
                        if xml.count('(') > xml.count(')'):
                            xml1 = re.sub(r'\)\s*\)\s*\)', ')) AS VALUE ) SELECT VALUE FROM CTE )', xml, flags=re.DOTALL | re.I)
                            from_manp = from_manp.replace(xml, xml1)
                        else:
                            xml1 = re.sub(r'\)\s*\)\s*\)', ')) AS VALUE ) SELECT VALUE FROM CTE ', xml, flags=re.DOTALL | re.I)
                            from_manp = from_manp.replace(xml, xml1)
                            from_manp = re.sub(r'\bCTE\s*\)\s*\)', 'CTE )', from_manp, flags=re.DOTALL | re.I)
                            data = data.replace(from_tab, from_manp)
                if check_wrd:
                    xml_stmt = re.findall(r'\(\s*SELECT\s*UNNEST\s*\(\s*XPATH\s*\(.*?\)\s*\)\s*\)\)', from_manp, flags=re.DOTALL | re.I)
                    for xml in xml_stmt:
                        if xml.count('(') > xml.count(')'):
                            xml1 = re.sub(r'\)\s*\)\s*\)\s*\)', '))) AS VALUE ) SELECT VALUE FROM CTE ', xml, flags=re.DOTALL | re.I)
                            from_manp = from_manp.replace(xml, xml1)
                        else:
                            xml1 = re.sub(r'\)\s*\)\s*\)\s*\)', '))) AS VALUE ) SELECT VALUE FROM CTE ', xml, flags=re.DOTALL | re.I)
                            from_manp = from_manp.replace(xml, xml1)
                            from_manp = re.sub(r'\bCTE\s*\)\s*\)', 'CTE )', from_manp, flags=re.DOTALL | re.I)
                            data = data.replace(from_tab, from_manp)
    return data